{"name": "three-card-monte", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@coinbase/cdp-sdk": "^1.9.0", "@coinbase/onchainkit": "latest", "@farcaster/frame-sdk": "^0.0.35", "@radix-ui/react-slot": "^1.2.2", "@tanstack/react-query": "^5", "@upstash/redis": "^1.34.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.508.0", "next": "^14.2.15", "react": "^18", "react-dom": "^18", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "viem": "2.23.12", "wagmi": "2.14.15"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "canvas-confetti": "latest", "eslint": "^8", "eslint-config-next": "14.2.15", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "framer-motion": "latest", "postcss": "^8", "prettier": "^3.5.3", "tailwindcss": "^3.4.1", "typescript": "^5"}, "overrides": {"viem": "2.23.12", "wagmi": "2.14.15"}}