{"name": "hangman-onchain", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@coinbase/onchainkit": "latest", "@coinbase/cdp-sdk": "^1.9.0", "@tanstack/react-query": "^5", "dotenv": "^16.5.0", "crypto": "^1.0.1", "next": "14.2.15", "react": "^18", "react-dom": "^18", "viem": "2.23.12", "wagmi": "2.14.15"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.15", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "overrides": {"viem": "2.23.12", "wagmi": "2.14.15"}}